# -*- coding: utf-8 -*-
import uuid
from calendar import c
from unittest import result
from venv import create
import tornado.ioloop
import pyrestful.rest
from db.model.hci.template import Template
from api.prometheus.client import Client as Pclient

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from util.tools import convert_to_bytes, generate_random_mac
from db.model.hci.compute import Domain, Host, Pool, Cluster, DomainDisk, DomainInterface
from db.model.hci.network import Switch, Router, RouterPort, RouterTable, SwitchPortGroups, SwitchPorts
from db.model.hci.user_resource_quota import(
    UserClusterAssignment, UserHostAssignment, UserStoragePool, 
    UserVmAssignment
)

from db.model.hci.user_resource_quota import (
    UserQuota
)

from db.model.user import User
from db.model.hci.storage import StoragePool, StorageVolume
from app.tasks.vm_tasks import (
    create_vm_callback,
    create_vm_callback_new,
    delete_vm_callback,
    distach_op_vm_open,
    distach_op_vm_close,
    distach_op_vm_destroy,
    distach_op_vm_pause,
    distach_op_vm_recover,
    distach_op_vm_reboot,
    distach_op_vm_restart,
    distach_op_vm_del,
    clone_vm_callback,
    create_start_vm_migration,
    start_vm_migration_callback,
    create_vm_snapshot_callback,
    restore_vm_snapshot_callback,
    delete_vm_snapshot_callback,
)
from app.agents.vm_tasks import (
    create_vm,
    clone_vm,
    delete_vm,
    create_vm_snapshot,
    delete_vm_snapshot,
    restore_vm_snapshot,
    snapshot_list,
    migrate_vm,
    check_migration_support,
    )
from app.tasks.disk_tasks import (
    create_sub_disk_callback, 
    create_sub_disk_with_template,
    
    )
from app.agents.disk_tasks import (
    create_sub_disk, 
    clone_disk,
    
    )
from app.tasks.network_tasks import (
    create_bridge_port_callback
    )
from app.agents.network_tasks import (
    domain_create_bridge_port
    )
from app.tasks.cdrom_tasks import create_cdrom
from math import ceil
from sqlalchemy import asc, desc
from api.log.log import CustomLogger
from celery import Celery, chord, group
from api.libvirt.client import Client as LClient
from settings import QUEUE_NAME
from util.tools import generate_unique_port_name

import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()

import random
import string
import os
import datetime
from util.decorators import role_required, deprecated_api

def generate_unique_vm_name(session, base_name, length=6):
    while True:
        rand_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))
        new_name = f"{base_name}_clone_{rand_str}"
        exists = session.query(Domain).filter(Domain.name == new_name).first()
        if not exists:
            return new_name

def generate_unique_volume_name(session, base_name, length=6):
    while True:
        rand_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))
        new_name = f"{base_name}_clone_{rand_str}"
        exists = session.query(StorageVolume).filter(StorageVolume.name == new_name).first()
        if not exists:
            return new_name

def get_new_volume_path(old_path, new_name, fmt):
    """
    生成新的卷路径
    old_path: 存储池路径或卷路径
    new_name: 新卷名
    fmt: 卷格式
    """
    # 如果 old_path 是文件路径（包含扩展名），则替换文件名
    if '.' in os.path.basename(old_path):
        dir_path = os.path.dirname(old_path)
        return f"{dir_path}/{new_name}.{fmt}"
    else:
        # 如果 old_path 是目录路径，则在其下创建新文件
        # 确保路径不以斜杠结尾
        clean_path = old_path.rstrip('/')
        return f"{clean_path}/{new_name}.{fmt}"
    
def get_old_volume_path(old_path, name, fmt):
    """
    生成新的卷路径
    old_path: 存储池路径或卷路径
    new_name: 新卷名
    fmt: 卷格式
    """
    # 如果 old_path 是文件路径（包含扩展名），则替换文件名
    if '.' in os.path.basename(old_path):
        dir_path = os.path.dirname(old_path)
        return old_path
    else:
        # 如果 old_path 是目录路径，则在其下创建新文件
        # 确保路径不以斜杠结尾
        clean_path = old_path.rstrip('/')
        return f"{clean_path}/{name}.{fmt}"
    
# def generate_mac_address():
#         """生成随机MAC地址，使用libvirt默认前缀52:54:00   52:54:00:34:34:df"""
#         mac = [0x52, 0x54, 0x00,
#                random.randint(0x00, 0xff),
#                random.randint(0x00, 0xff),
#                random.randint(0x00, 0xff)]
#         return ':'.join(map(lambda x: "%02x" % x, mac))


def operator_can_create_vm_on_host(session, username, host_id, cluster_id):
    # 查用户
    user = session.query(User).filter(User.username == username).first()
    if not user:
        return False, {"msg": "未找到用户", "code": 404}


    # 联合查询：用户-主机绑定 或 用户-集群绑定且主机属于该集群
    host_assignment_exists = session.query(UserHostAssignment).filter(
        UserHostAssignment.user_id == user.id,
        UserHostAssignment.host_id == host_id
    ).exists()

    cluster_assignment_exists = session.query(UserClusterAssignment).filter(
        UserClusterAssignment.user_id == user.id,
        UserClusterAssignment.cluster_id == cluster_id
    ).exists()

    # 只查一次数据库
    allowed = session.query(host_assignment_exists | cluster_assignment_exists).scalar()
    if allowed:
        return True, user

    return False, {"msg": "无权在该主机上创建虚拟机", "code": 403}

def operator_can_operate_vm(session, username, vm_id):
    # 查用户
    user = session.query(User).filter(User.username == username).first()
    if not user:
        return False, {"msg": "未找到用户", "code": 404}

    # 联合查询：用户-虚拟机绑定
    assignment_exists = session.query(UserVmAssignment).filter(
        UserVmAssignment.user_id == user.id,
        UserVmAssignment.vm_id == vm_id
    ).exists()

    # 只查一次数据库
    allowed = session.query(assignment_exists).scalar()
    if allowed:
        return True, user

    return False, {"msg": "无权对虚拟机进行该操作", "code": 403}

def operator_can_operate_vms(session, username, vms, op="操作"):
    # 查用户
    user = session.query(User).filter(User.username == username).first()
    if not user:
        return False, {"msg": "未找到用户", "code": 404}

    unauthorized_vms = []
    for vm in vms:
        vm_id = vm.id if isinstance(vm, Domain) else vm
        # 如果传入的是Domain对象，直接用name属性，否则需要查询Domain获取name
        if isinstance(vm, Domain):
            vm_name = vm.name
        else:
            domain = session.query(Domain).filter(Domain.id == vm_id).first()
            vm_name = domain.name if domain else str(vm_id)

        # 联合查询：用户-虚拟机绑定
        assignment_exists = session.query(UserVmAssignment).filter(
            UserVmAssignment.user_id == user.id,
            UserVmAssignment.vm_id == vm_id
        ).exists()

        # 只查一次数据库
        allowed = session.query(assignment_exists).scalar()
        if not allowed:
            unauthorized_vms.append(vm_name)

    # 如果有未授权的虚拟机，返回所有未授权的虚拟机名称
    if unauthorized_vms:
        return False, {
            "msg": f"无权{op}以下虚拟机: {', '.join(unauthorized_vms)}", 
            "code": 403
        }

    # 所有虚拟机都有权限操作
    return True, user

def bytes_to_gb(bytes_value, ndigits=2):
    return round(bytes_value / (1024 ** 3), ndigits)

class VmHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @role_required(("sysadm","supadm","operator"))
    @get(_path="/v5/vm/overview/{_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_host_overview(self, _id):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username')
        data = {}
        

        with self.session_scope() as session:
            if role == "operator":
                # 1. 根据账号名查用户信息
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return {"msg": "未找到用户", "code": 404}
                # 2. 判断用户是否绑定该虚拟机
                assignment = session.query(UserVmAssignment).filter(
                    UserVmAssignment.user_id == user.id,
                    UserVmAssignment.vm_id == _id
                ).first()
                if not assignment:
                    return {"msg": "无权访问该虚拟机", "code": 403}
            vm = session.query(Domain).filter(Domain.id == _id).first()
            if not vm:
                return {"msg": "虚拟机不存在", "code": 404}
                
            # data["vm_count"] = str(len(vms))
            logic_cpu_count = 0
            logic_mem_count = 0
            logic_disk_count = 0
            # for vm in vms:
            logic_cpu_count += int(vm.vcpu)
            logic_mem_count += int(vm.memory)
            data = vm.to_dict_merge()
        

            # Step 5: 查询所有 物理cpu，逻辑cpu，内存，磁盘
            # data["logic_cpu_count"] = str(logic_cpu_count)
            # data["logic_cpu_rate"] = str(0)
            # data["logic_mem_count"] = str(logic_mem_count)
            # data["logic_disk_count"] = str(logic_disk_count) # 存疑

            c = Pclient()
            hosts = []
            ip_pattern_parts = [f"{d['ip']}:9100" for d in hosts]
            ip_regex = "|".join(ip_pattern_parts)
            # all_cpu_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            all_cpu_hz = c.query_vector_by_query(f'sum(node_cpu_scaling_frequency_hertz{{job="node_exporter", instance=~"{ip_regex}"}})')
            use_cpu_hz = c.query_vector_by_query(f'sum((node_cpu_scaling_frequency_hertz{{job="node_exporter", instance=~"{ip_regex}"}} / node_cpu_frequency_max_hertz{{job="node_exporter", instance=~"{ip_regex}"}}))')
            if all_cpu_hz:
                data["cpu_all_hz"] = all_cpu_hz[0]["value"][1]
                data["cpu_use_hz"] = use_cpu_hz[0]["value"][1]
            else:
                data["cpu_all_hz"] = 0
                data["cpu_use_hz"] = 0

            all_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            use_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}}) - sum(node_memory_MemAvailable_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            if all_mem_count:
                data["mem_all_count"] = all_mem_count[0]["value"][1]
                data["mem_use_count"] = use_mem_count[0]["value"][1]
            else:
                data["mem_all_count"] = 0
                data["mem_use_count"] = 0

            all_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            use_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})- sum(node_filesystem_free_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            if all_disk_count:
                data["disk_all_count"] = all_disk_count[0]["value"][1]
                data["disk_use_count"] = use_disk_count[0]["value"][1]
            else:
                data["disk_all_count"] = 0
                data["disk_use_count"] = 0

        return {"msg": "ok", "code": 200, "data": data}

    @role_required(("sysadm","supadm","operator"))
    @get(_path="/v5/vm/detail/{domain_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_vm_detail(self, domain_id):

        with self.session_scope() as session:
            # 查询虚机
            domain = session.query(Domain).join(Host, Domain.host_id == Host.id).filter(Domain.id == domain_id).first()
            if domain:
                data = Domain.to_dict_merge(domain)
                return data

        return {"msg": "没有找到虚机", "code": "404"}

    @deprecated_api()
    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_vm_list(self, form):

        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')
        recycle = form.get('recycle', False)

        with self.session_scope() as session:
            if recycle:
                query = session.query(Domain).filter(Domain.domain_recycle_id == "1")
            if not recycle:
                query = session.query(Domain).filter(Domain.domain_recycle_id == "0")

            # 搜索功能，根据指定字段进行搜索（这里假设搜索的字段为 name）
            if search_str:
                query = query.filter(Domain.name.ilike(f"%{search_str}%"))

            # 排序功能
            if order_by:
                if order_type == 'asc':
                    query = query.order_by(asc(getattr(Domain, order_by)))
                else:
                    query = query.order_by(desc(getattr(Domain, order_by)))

            # 计算总记录数
            total_records = query.count()

            # 分页
            domains = query.limit(per_page).offset((page - 1) * per_page).all()

            # 将结果转换为字典列表
            domain_list = [Domain.to_dict(domain) for domain in domains]

            # 计算总页数
            total_pages = ceil(int(total_records) / per_page)
            # 返回分页信息和数据
        return {
            "msg": "获取虚机列表成功",
            "code": 200,
            "total": total_records,
            "data": domain_list
        }

        # return {"msg": "获取虚机列表失败", "code": 500}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_vm_list(self, form):
        
        total_records = 0
        domain_list = 0

        role = self.get_cookie("role")
        username = self.get_cookie("username")
        
        
        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')
        recycle = form.get('recycle', False)
        type = form.get('type', 'recource')
        _id = form.get('_id', '')
        
        if role == "sysadm" or role == "supadm":

            with self.session_scope() as session:
                # 首先创建基础查询
                query = session.query(Domain)

                # 根据 recycle 状态过滤
                recycle_value = "1" if recycle else "0"
                query = query.filter(Domain.domain_recycle_id == recycle_value)

                # 根据不同类型进行过滤
                if type == 'recource':
                    # 获取所有相关的域
                    query = query.join(Host).join(Cluster).join(Pool)

                elif type == 'pool':
                    query = query.join(Host).join(Cluster).join(Pool).filter(Pool.id == _id)

                elif type == 'cluster':
                    query = query.join(Host).join(Cluster).filter(Cluster.id == _id)

                elif type == 'host':
                    query = query.join(Host).filter(Host.id == _id)

                # 添加搜索条件
                if search_str:
                    query = query.filter(Domain.name.ilike(f"%{search_str}%"))

                # 添加排序
                if hasattr(Domain, order_by):
                    order_column = getattr(Domain, order_by)
                    if order_type == 'asc':
                        query = query.order_by(asc(order_column))
                    else:
                        query = query.order_by(desc(order_column))

                # 获取总记录数
                total_records = query.count()

                # 执行分页
                domains = query.offset((page - 1) * per_page).limit(per_page).all()

                # 将结果转换为字典列表
                domain_list = [domain.to_dict() for domain in domains]

                # # 计算总页数
                # total_pages = ceil(int(total_records) / per_page)
                new_logger.log(
                    self.username, "虚机管理", "查询虚机", "成功", "role", "{}:{},成功".format("查询虚拟机", "")
                )
                
        if role == "operator":
            with self.session_scope() as session:
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return {"msg": "用户不存在", "code": 404}
                user_id = str(user.id)
                vm_assignments = session.query(UserVmAssignment).filter(UserVmAssignment.user_id == user_id).all()
                vm_ids = [assignment.vm_id for assignment in vm_assignments]
                query = session.query(Domain).filter(Domain.id.in_(vm_ids))
                # recycle 过滤
                recycle_value = "1" if recycle else "0"
                query = query.filter(Domain.domain_recycle_id == recycle_value)
                # 搜索
                if search_str:
                    query = query.filter(Domain.name.ilike(f"%{search_str}%"))
                # 排序
                if hasattr(Domain, order_by):
                    order_column = getattr(Domain, order_by)
                    if order_type == 'asc':
                        query = query.order_by(asc(order_column))
                    else:
                        query = query.order_by(desc(order_column))
                total_records = query.count()
                domains = query.offset((page - 1) * per_page).limit(per_page).all()
                domain_list = [domain.to_dict() for domain in domains]
        
        return {
                    "msg": "获取虚机列表成功",
                    "code": 200,
                    "total": total_records,
                    "data": domain_list
                }

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_vm_create(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie("username")

        vm_name = form.get("vm_name", "")
        host_ip = form.get("host_ip", "")
        form["domain_id"] = str(uuid.uuid4())
        form["username"] = username
        form["role"] = role
        
        if role == "sysadm" or role == "supadm":
            
            # 查出host信息 创建虚机空壳 但是不启动 由celery任务来启动
            with self.session_scope() as session:
                # 根据host_ip查出host信息
                host = session.query(Host).filter(Host.ip == host_ip).first()
                if not host:
                    return {"status": "failed", "message": "主机不存在"}

                form["host_id"] = str(host.id)
                domain = Domain()
                domain.id = form.get("domain_id", "")
                domain.name = vm_name
                domain.os_type = form.get("os_type", 0)
                domain.host_id = str(host.id)
                domain.cluster_id = host.cluster_id
                domain.pool_id = host.pool_id
                domain.status = "creating"
                domain.vcpu = form.get("vcpu_unit", 1)
                domain.memory = form.get("memory_unit", 5)

                domain.os_version = "ubuntu"
                domain.remark = "remark"
                domain.is_ha = 0
                domain.auto_migrate = 0
                domain.is_losing_contact = 0
                domain.domainname = vm_name
                domain.uuid = "34343"
                domain.hmemory = form.get("memory_unit", 5)
                domain.cpu_arch = "x86"
                domain.vnc_port = "0000"
                domain.spice_port = "0000"
                domain.bind_ip_list = "dhcp"
                domain.is_persistence = 1
                domain.secret_id = "0"
                domain.secret_alg = "0"
                domain.domain_recycle_id = "0"
                domain.safe_status = 0
                domain.is_hide = 0
                domain.defense_status = 0
                domain.is_ignore = 0
                session.add(domain)
                session.commit()
                
                
                disks_form = []
                for disk in form.get("disk", []):
                    # 检查卷是否存在
                    disk_id = disk.get("disk_id", "")
                    pool_id = disk.get("pool_id", "")
                    path = disk.get("path", "")
                    disk_type = disk.get("disk_type", "")
                    # if not disk_type or disk_type == "file":
                    #     disk["disk_type"] = 'qcow2'
                    #     disk_type = 'qcow2'
                    disk_name = disk.get("disk_name", "")
                    size = disk.get("size", 0)
                    disk_unit_type = disk.get("disk_unit_type", "GB")
                    capacity = convert_to_bytes(size, disk_unit_type)
                    storage_pool = session.query(StoragePool).filter_by(
                            id=pool_id).first()
                    if storage_pool is None:
                            return {"code": 200, "msg": "存储池不存在"}
                    disk["storage_type_code"] = disk.get("storage_type_code", "qcow2")   
                    disk["capacity"] = capacity
                    disk["type_code"] = storage_pool.type_code
                    disk["pool_name"] = storage_pool.name
                    # 将数据存入数据表中
                    if path == "":
                        disk["path"] = storage_pool.storage_local_dir
                        path = storage_pool.storage_local_dir
                    if not disk_id:
                        disk["role"] = role
                        disk["username"] = username
                        # 存储卷不存在，进行创建
                        disk["is_exist"] = False
                        data = {
                            "name": disk_name,
                            "storage_pool_id": pool_id,
                            # "type_code": form["volume_format"],
                            "join_type": 3,
                            "path": path,
                            "encrypt": 0,
                            "status": 4,
                            "capacity": capacity,
                            "allocation": capacity,
                            "preallocation": 1,
                            "protocol_type": "",
                            "volume_type": "qcow2",

                        }
                        volume = StorageVolume.from_dict(data)
                        session.add(volume)
                        session.commit()
                        disk["disk_id"] = volume.id
                    else:
                        volume = session.query(StorageVolume).filter_by(id=disk_id).first()
                        disk["storage_type_code"] = volume.volume_type
                        disk["is_exist"] = True
                    
                    disks_form.append(disk)

                # cdrom = form.get("cdrom", "")
                # for i in cdrom:
                #     volume = session.query(StorageVolume).filter_by(id=disk_id).first()
                    
                
                # form["disk"] = disks_form    
                
                interface = form.get("interface", "")
                nets_form = []
                for i in interface:
                    net_form = i
                    ip = i.get("ip", "")
                    mac = i.get("mac", "")
                    if mac == "":
                        mac = generate_random_mac()
                    net_form["mac"] = mac
                    switch_port_id = i.get("switch_port_id", "")
                
                    switch_port_group_id = i.get("net_id", "")
                    # 通过 switch_port_group_id 获取端口组及其关联的交换机信息
                    result = session.query(SwitchPortGroups, Switch) \
                        .join(Switch, SwitchPortGroups.switchs_id == Switch.id) \
                        .filter(SwitchPortGroups.id == switch_port_group_id) \
                        .first()

                    if not result:
                        raise ValueError(f"未找到端口组 {switch_port_group_id} 或其关联的交换机")
                    port_group, switch = result  # 解构结果
                    port_name = generate_unique_port_name()
                    
                    if not switch_port_id:
                        net_form["role"] = role
                        net_form["username"] = username
                        # 新建端口数据库信息
                        switch_port = SwitchPorts()
                        switch_port.name = port_name
                        switch_port.switchs_id = str(switch.id)
                        switch_port.switch_port_group_id = switch_port_group_id
                        switch_port.ip = ip
                        session.add(switch_port)
                        session.commit()
                        switch_port_id = switch_port.id
                    net_form["switch_port_id"] = switch_port_id
                    net_form['port_name'] = port_name
                    net_form['switch'] = switch.to_dict()
                    net_form['switch_group'] = port_group.to_dict()
                    net_form['switch_id'] = str(switch.id)
                    net_form['switch_port_group_id'] = switch_port_group_id 
                    net_form["network"] = switch.ovs_name
                    nets_form.append(net_form)
        if role == "operator":
            # 查用户
            user = session.query(User).filter(User.username == username).first()
            if not user:
                return False, {"msg": "未找到用户", "code": 200}
            # 查出host信息 创建虚机空壳 但是不启动 由celery任务来启动
            with self.session_scope() as session:
                # 操作员作用户权限判断
                
                
                # 根据host_ip查出host信息
                host = session.query(Host).filter(Host.ip == host_ip).first()
                if not host:
                    return {"status": "failed", "message": "主机不存在"}
                
                ok, result = operator_can_create_vm_on_host(session, username, host.id, host.cluster_id)
                if not ok:
                    return result
                
                # 获取用户配额，进行判断
                user_quota = session.query(UserQuota).filter(UserQuota.user_id == user.id).first()
                if not user_quota:
                    return {"code": 200, "msg": "用户未设置配额，无法新建虚拟机"}
                
                # 获取本次申请的资源
                vcpu = int(form.get("vcpu_unit", 1))
                memory = int(form.get("memory_unit", 5))  # 单位MB

                # 判断 CPU 配额
                if (user_quota.cpu_used or 0) + vcpu > user_quota.cpu_limit:
                    return {
                        "code": 200,
                        "msg": f"CPU配额不足，当前已用{user_quota.cpu_used}核，申请新建{vcpu}核，超出上限{user_quota.cpu_limit}核"
                    }

                # 判断内存配额
                if (user_quota.memory_used or 0) + memory > user_quota.memory_limit:
                    return {
                        "code": 200,
                        "msg": f"内存配额不足，当前已用{user_quota.memory_used}MB，申请新建{memory}MB，超出上限{user_quota.memory_limit}MB"
                    }

                # 通过配额校验，后续可继续创建虚拟机
                
                # 新建磁盘累计容量
                new_disk_cap = 0
                
                for disk in form.get("disk", []):
                    # 检查卷是否存在
                    disk_id = disk.get("disk_id", "")
                    if disk_id:
                        # 存储卷存在，不做配额判断，后续都是对于新建的卷的判断
                        # print("存储卷存在，不做配额判断")
                        continue
                    pool_id = disk.get("pool_id", "")
                    path = disk.get("path", "")
                    disk_name = disk.get("disk_name", "")
                    size = disk.get("size", 0)
                    disk_unit_type = disk.get("disk_unit_type", "GB")
                    capacity = convert_to_bytes(size, disk_unit_type)
                    
                    # 判断存储池是否存在
                    storage_pool = session.query(StoragePool).filter_by(
                            id=pool_id).first()
                    if storage_pool is None:
                            return {"code": 200, "msg": f"存储池 {storage_pool.name} 不存在"}
                    
                    # 判断用户是否拥有存储池的权限

                    # 查用户-存储池绑定
                    pool_assignment = session.query(UserStoragePool).filter(
                        UserStoragePool.user_id == user.id,
                        UserStoragePool.pool_id == pool_id
                    ).first()
                    if not pool_assignment:
                        return {"code": 200, "msg": f"用户没有访问存储池 {storage_pool.name} 的权限"}
                    
                    # 记录新建磁盘的累计容量
                    new_disk_cap += capacity
                    
                
                # 循环结束后，进行配额判断
                # 计算新总用量
                total_storage_used = (user_quota.storage_used or 0) + new_disk_cap

                if total_storage_used > user_quota.storage_limit:
                    used_gb = bytes_to_gb(user_quota.storage_used)
                    new_gb = bytes_to_gb(new_disk_cap)
                    limit_gb = bytes_to_gb(user_quota.storage_limit)
                    return {
                        "code": 200,
                        "msg": f"存储配额不足，当前已用{used_gb}GB，申请新建{new_gb}GB，超出上限{limit_gb}GB"
                    }
                             
                # 虚拟机创建成功后，更新配额信息，cpu 和内存， 存储的配额在存储卷创建成功后更新   
                form["user_quota_id"] = user_quota.id
                    

                form["host_id"] = str(host.id)
                domain = Domain()
                domain.id = form.get("domain_id", "")
                domain.name = vm_name
                domain.os_type = form.get("os_type", 0)
                domain.host_id = str(host.id)
                domain.cluster_id = host.cluster_id
                domain.pool_id = host.pool_id
                domain.status = "creating"
                domain.vcpu = form.get("vcpu_unit", 1)
                domain.memory = form.get("memory_unit", 5)

                domain.os_version = "ubuntu"
                domain.remark = "remark"
                domain.is_ha = 0
                domain.auto_migrate = 0
                domain.is_losing_contact = 0
                domain.domainname = vm_name
                domain.uuid = "34343"
                domain.hmemory = form.get("memory_unit", 5)
                domain.cpu_arch = "x86"
                domain.vnc_port = "0000"
                domain.spice_port = "0000"
                domain.bind_ip_list = "dhcp"
                domain.is_persistence = 1
                domain.secret_id = "0"
                domain.secret_alg = "0"
                domain.domain_recycle_id = "0"
                domain.safe_status = 0
                domain.is_hide = 0
                domain.defense_status = 0
                domain.is_ignore = 0
                session.add(domain)
                session.commit()
                
                # 增加用户和虚拟机的绑定关系
                assignment = UserVmAssignment(
                        id=str(uuid.uuid4()),
                        user_id=user.id,
                        vm_id=domain.id
                    )
                session.add(assignment)
                
                
                disks_form = []
                for disk in form.get("disk", []):
                    # 检查卷是否存在
                    disk_id = disk.get("disk_id", "")
                    pool_id = disk.get("pool_id", "")
                    path = disk.get("path", "")
                    disk_type = disk.get("disk_type", "")
                    # if not disk_type or disk_type == "file":
                    #     disk["disk_type"] = 'qcow2'
                    #     disk_type = 'qcow2'
                    disk_name = disk.get("disk_name", "")
                    size = disk.get("size", 0)
                    disk_unit_type = disk.get("disk_unit_type", "GB")
                    capacity = convert_to_bytes(size, disk_unit_type)
                    storage_pool = session.query(StoragePool).filter_by(
                            id=pool_id).first()
                    if storage_pool is None:
                            return {"code": 200, "msg": "存储池不存在"}
                    disk["storage_type_code"] = disk.get("storage_type_code", "qcow2")   
                    disk["capacity"] = capacity
                    disk["type_code"] = storage_pool.type_code
                    disk["pool_name"] = storage_pool.name
                    # 将数据存入数据表中
                    if path == "":
                        disk["path"] = storage_pool.storage_local_dir
                        path = storage_pool.storage_local_dir
                    if not disk_id:
                        # 存储卷不存在，进行创建
                        disk["role"] = role
                        disk["username"] = username
                        disk["is_exist"] = False
                        data = {
                            "name": disk_name,
                            "storage_pool_id": pool_id,
                            # "type_code": form["volume_format"],
                            "join_type": 3,
                            "path": path,
                            "encrypt": 0,
                            "status": 4,
                            "capacity": capacity,
                            "allocation": capacity,
                            "preallocation": 1,
                            "protocol_type": "",
                            "volume_type": "qcow2",

                        }
                        volume = StorageVolume.from_dict(data)
                        session.add(volume)
                        session.commit()
                        disk["disk_id"] = volume.id
                        disk["user_quota_id"] = user_quota.id # 新卷创建成功后，需要更新配额信息
                    else:
                        volume = session.query(StorageVolume).filter_by(id=disk_id).first()
                        disk["storage_type_code"] = volume.volume_type
                        disk["is_exist"] = True
                    
                    disks_form.append(disk)

                # cdrom = form.get("cdrom", "")
                # for i in cdrom:
                #     volume = session.query(StorageVolume).filter_by(id=disk_id).first()
                    
                
                # form["disk"] = disks_form    
                
                interface = form.get("interface", "")
                nets_form = []
                for i in interface:
                    net_form = i
                    ip = i.get("ip", "")
                    mac = i.get("mac", "")
                    if mac == "":
                        mac = generate_random_mac()
                    net_form["mac"] = mac
                    switch_port_id = i.get("switch_port_id", "")
                
                    switch_port_group_id = i.get("net_id", "")
                    # 通过 switch_port_group_id 获取端口组及其关联的交换机信息
                    result = session.query(SwitchPortGroups, Switch) \
                        .join(Switch, SwitchPortGroups.switchs_id == Switch.id) \
                        .filter(SwitchPortGroups.id == switch_port_group_id) \
                        .first()

                    if not result:
                        raise ValueError(f"未找到端口组 {switch_port_group_id} 或其关联的交换机")
                    port_group, switch = result  # 解构结果
                    port_name = generate_unique_port_name()
                    
                    if not switch_port_id:
                        net_form["role"] = role
                        net_form["username"] = username
                        # 新建端口数据库信息
                        switch_port = SwitchPorts()
                        switch_port.name = port_name
                        switch_port.switchs_id = str(switch.id)
                        switch_port.switch_port_group_id = switch_port_group_id
                        switch_port.ip = ip
                        session.add(switch_port)
                        session.commit()
                        switch_port_id = switch_port.id
                    net_form["switch_port_id"] = switch_port_id
                    net_form['port_name'] = port_name
                    net_form['switch'] = switch.to_dict()
                    net_form['switch_group'] = port_group.to_dict()
                    net_form['switch_id'] = str(switch.id)
                    net_form['switch_port_group_id'] = switch_port_group_id 
                    net_form["network"] = switch.ovs_name
                    nets_form.append(net_form)
        form["interface"] = nets_form

        # 异步调用多任务集合点来完成
        # TODO
        queue = "queue_" + host_ip
        
                
        # 子任务链：创建磁盘 + 更新数据库（使用默认队列）
        disk_chain = (
            create_sub_disk.s(vm_name, disks_form).set(queue=queue) |
            create_sub_disk_callback.s().set(queue=QUEUE_NAME)
        )
        
        # 子任务链：创建桥接端口 + 更新数据库（使用默认队列）
        bridge_chain = (
            domain_create_bridge_port.s(nets_form).set(queue=queue) |
            create_bridge_port_callback.s().set(queue=QUEUE_NAME)
        )

        # 构建子任务组
        tasks = group([
            disk_chain,
            bridge_chain,
        ])

        # 创建最终回调链：先执行 create_vm_callback，再执行 update_database_callback（使用默认队列）
        final_chain = (
            create_vm.s(form=form).set(queue=queue) |
            create_vm_callback.s().set(queue=QUEUE_NAME)
        )

        # 启动 chord 任务
        result = chord(tasks)(final_chain)
        # tasks = [create_sub_disk.s(vm_name, form), create_bridge_port.s(vm_name, form)]
        # result = chord(tasks)(create_vm_callback_new.s(form))
        # new_logger.log(
        #     self.username, "虚机管理", "创建虚机", "成功", "role", "{}:{},失败".format("创建虚拟机", vm_name)
        # )
        return {"msg": "已开始创建虚机", "code": 200}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/create/use/template", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_vm_create_with_template(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie("username")

        vm_name = form.get("vm_name", "")
        host_ip = form.get("host_ip", "")
        template_id = form.get("template_id", "")
        form["domain_id"] = str(uuid.uuid4())
        form["username"] = username
        form["role"] = role

        # 先查询模板信息，供后续使用
        with self.session_scope() as session:
            template = session.query(Template).filter(Template.id == template_id).first()
            if not template:
                return {"status": "failed", "message": "模板不存在"}

            # 将模板信息转换为字典，避免session关闭后无法访问
            template_dict = {
                "id": str(template.id),
                "name": template.name,
                "vcpu": template.vcpu,
                "cpu_arch": template.cpu_arch,
                "memory": template.memory,
                "memory_unit": template.memory_unit,
                "disk_path": template.disk_path,
                "disk_name": template.disk_name,
                "disk_type": template.disk_type,
                "disk_type_code": template.disk_type_code,
                "network": template.network
            }

        if role == "sysadm" or role == "supadm":
            # 管理员权限，直接创建
            with self.session_scope() as session:
                # 根据host_ip查出host信息
                host = session.query(Host).filter(Host.ip == host_ip).first()
                if not host:
                    return {"status": "failed", "message": "主机不存在"}

                form["host_id"] = str(host.id)
                domain = Domain()
                domain.id = form.get("domain_id", "")
                domain.name = vm_name
                domain.os_type = form.get("os_type", 0)
                domain.host_id = str(host.id)
                domain.cluster_id = host.cluster_id
                domain.pool_id = host.pool_id
                domain.status = "creating"
                domain.vcpu = template_dict["vcpu"]
                domain.memory = template_dict["memory"]
                domain.os_version = form.get("os_version", "ubuntu")
                domain.remark = form.get("remark", "")
                domain.is_ha = form.get("is_ha", 0)
                domain.auto_migrate = form.get("auto_migrate", 0)
                domain.is_losing_contact = 0
                domain.domainname = form.get("domainname", "instance")
                domain.uuid = str(uuid.uuid4())
                domain.hmemory = template_dict["memory"]
                domain.cpu_arch = template_dict["cpu_arch"]
                domain.vnc_port = "0000"
                domain.spice_port = "0000"
                domain.bind_ip_list = form.get("bind_ip_list", "dhcp")
                domain.is_persistence = form.get("is_persistence", 1)
                domain.secret_id = "0"
                domain.secret_alg = "0"
                domain.domain_recycle_id = "0"
                domain.safe_status = 0
                domain.is_hide = 0
                domain.defense_status = 0
                domain.is_ignore = 0
                session.add(domain)
                session.commit()

        elif role == "operator":
            # 操作员权限，需要进行权限和配额检查
            with self.session_scope() as session:
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return {"msg": "未找到用户", "code": 200}
                # 根据host_ip查出host信息
                host = session.query(Host).filter(Host.ip == host_ip).first()
                if not host:
                    return {"status": "failed", "message": "主机不存在"}

                # 操作员权限判断
                ok, result = operator_can_create_vm_on_host(session, username, host.id, host.cluster_id)
                if not ok:
                    return result

                # 获取用户配额，进行判断
                user_quota = session.query(UserQuota).filter(UserQuota.user_id == user.id).first()
                if not user_quota:
                    return {"code": 200, "msg": "用户未设置配额，无法新建虚拟机"}

                # 检查CPU配额
                vcpu_count = int(template_dict["vcpu"])
                total_cpu_used = (user_quota.cpu_used or 0) + vcpu_count
                if total_cpu_used > user_quota.cpu_limit:
                    return {
                        "code": 200,
                        "msg": f"CPU配额不足，当前已用{user_quota.cpu_used}核，申请新建{vcpu_count}核，超出上限{user_quota.cpu_limit}核"
                    }

                # 检查内存配额
                memory_mb = int(template_dict["memory"])
                total_memory_used = (user_quota.memory_used or 0) + memory_mb
                if total_memory_used > user_quota.memory_limit:
                    return {
                        "code": 200,
                        "msg": f"内存配额不足，当前已用{user_quota.memory_used}MB，申请新建{memory_mb}MB，超出上限{user_quota.memory_limit}MB"
                    }

                form["host_id"] = str(host.id)
                form["user_quota_id"] = str(user_quota.id)
                domain = Domain()
                domain.id = form.get("domain_id", "")
                domain.name = vm_name
                domain.os_type = form.get("os_type", 0)
                domain.host_id = str(host.id)
                domain.cluster_id = host.cluster_id
                domain.pool_id = host.pool_id
                domain.status = "creating"
                domain.vcpu = template_dict["vcpu"]
                domain.memory = template_dict["memory"]
                domain.os_version = form.get("os_version", "ubuntu")
                domain.remark = form.get("remark", "")
                domain.is_ha = form.get("is_ha", 0)
                domain.auto_migrate = form.get("auto_migrate", 0)
                domain.is_losing_contact = 0
                domain.domainname = form.get("domainname", "instance")
                domain.uuid = str(uuid.uuid4())
                domain.hmemory = template_dict["memory"]
                domain.cpu_arch = template_dict["cpu_arch"]
                domain.vnc_port = "0000"
                domain.spice_port = "0000"
                domain.bind_ip_list = form.get("bind_ip_list", "dhcp")
                domain.is_persistence = form.get("is_persistence", 1)
                domain.secret_id = "0"
                domain.secret_alg = "0"
                domain.domain_recycle_id = "0"
                domain.safe_status = 0
                domain.is_hide = 0
                domain.defense_status = 0
                domain.is_ignore = 0
                session.add(domain)
                session.commit()

                # 创建用户虚拟机关联
                user_vm_assignment = UserVmAssignment()
                user_vm_assignment.id = str(uuid.uuid4())
                user_vm_assignment.user_id = user.id
                user_vm_assignment.vm_id = domain.id
                session.add(user_vm_assignment)
                session.commit()

        # 从模板构建磁盘和网络配置
        disk_template_dict = {
            "disk_path": template_dict["disk_path"],
            "disk_name": template_dict["disk_name"],
            "disk_type": template_dict["disk_type"],
            "host_ip": host_ip
        }

        # 构建网络配置（使用模板中的网络配置）
        nets_form = []
        if template_dict["network"]:
            nets_form.append({
                "network_name": template_dict["network"],
                "vm_name": vm_name
            })

        # 异步调用多任务集合点来完成
        queue = "queue_" + host_ip

        # 子任务链：创建磁盘 + 更新数据库（使用默认队列）
        disk_chain = (
            create_sub_disk_with_template.s(vm_name, disk_template_dict).set(queue=queue) |
            create_sub_disk_callback.s().set(queue=QUEUE_NAME)
        )

        # 子任务链：创建桥接端口 + 更新数据库（使用默认队列）
        bridge_chain = (
            domain_create_bridge_port.s(nets_form).set(queue=queue) |
            create_bridge_port_callback.s().set(queue=QUEUE_NAME)
        )

        # 构建子任务组
        tasks = group([
            disk_chain,
            bridge_chain,
        ])

        # 创建最终回调链：先执行 create_vm_callback，再执行 update_database_callback（使用默认队列）
        final_chain = (
            create_vm.s(form=form).set(queue=queue) |
            create_vm_callback.s().set(queue=QUEUE_NAME)
        )

        # 启动 chord 任务
        result = chord(tasks)(final_chain)
        return {"msg": "已开始创建虚机", "code": 200}

    @role_required(("sysadm","supadm"))
    @post(_path="/v5/vm/op/full/clone", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_full_clone_vm(self, form):
        """
        克隆虚拟机
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie("username", "")

        vm_id = form.get("vm_id", "")
        vm_name = form.get("vm_name", [])
        
        form["role"] = role
        form["username"] = username
        form["clone_tag"] = "full"
        with self.session_scope() as session:
            domain = session.query(Domain).filter(Domain.id == vm_id).first()
            host_ip = domain.host.ip
            queue = "queue_" + host_ip
            
            # 克隆虚拟机数据库数据创建
            # 查询原虚拟机
            if not domain:
                return {"status": "200", "msg": "原虚拟机不存在"}

            # 复制原虚拟机所有字段
            domain_data = domain.to_dict() if hasattr(domain, 'to_dict') else domain.__dict__.copy()

            # 生成新 id、uuid、name
            new_domain_id = str(uuid.uuid4())
            new_uuid = str(uuid.uuid4())
            new_vm_name = generate_unique_vm_name(session, domain.name)

            # 更新必要字段
            domain_data["id"] = new_domain_id
            domain_data["uuid"] = new_uuid
            domain_data["name"] = new_vm_name
            domain_data["status"] = "creating"
            domain_data["domainname"] = new_vm_name
            # 其他如 created_at、operation_time 可重置为 now

            # 移除 SQLAlchemy 内部字段
            domain_data.pop("_sa_instance_state", None)

            # 新建并插入
            new_domain = Domain(**domain_data)
            session.add(new_domain)
            session.commit()
            
            
            
             # 克隆磁盘部分
            domain_disks = session.query(DomainDisk).filter(DomainDisk.domain_id == vm_id).all()
            new_disks = []
            for disk in domain_disks:
                volume = session.query(StorageVolume).filter(StorageVolume.id == disk.storage_vol_id).first()
                if not volume:
                    continue
                new_name = generate_unique_volume_name(session, volume.name)
                fmt = volume.volume_type or "qcow2"
                new_path = get_new_volume_path(volume.path, new_name, fmt)
                new_volume = StorageVolume(
                    name=new_name,
                    storage_pool_id=volume.storage_pool_id,
                    path=new_path,
                    volume_type=fmt,
                    protocol_type=volume.protocol_type,
                    join_type=volume.join_type,
                    encrypt=volume.encrypt,
                    status=volume.status,
                    capacity=volume.capacity,
                    allocation=volume.allocation,
                    preallocation=volume.preallocation,
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now(),
                    remark="克隆卷",
                )
                session.add(new_volume)
                session.flush()  # 获取新卷id
                old_path = get_old_volume_path(volume.path, volume.name, volume.volume_type)
                new_disks.append({
                    "old_domain_disk": disk.to_dict(),
                    "old_path": old_path,
                    "new_volume_id": new_volume.id,
                    "new_volume_name": new_name,
                    "new_path": new_path,
                    "volume_type": fmt,
                    "clone_type": "full",  # 默认完整克隆
                    "storage_pool_id": volume.storage_pool_id,
                    # 直接补充XML拼接所需参数
                    "disk_name": new_name,
                    "path": new_path,
                    "storage_type_code": fmt,
                    "disk_type": disk.type_code,
                    "bus": disk.bus,
                    "dev": disk.dev,
                    "device": disk.device,
                    "qemu_type": disk.qemu_type,
                    "boot_order": disk.boot_order,
                    "capacity": volume.capacity,
                    "allocation": volume.allocation,
                })
            session.commit()

            # 建立新DomainDisk关联
            for disk in new_disks:
                old_disk = disk["old_domain_disk"]
                new_domain_disk = DomainDisk(
                    domain_id=new_domain_id,  # 新虚拟机id
                    host_id=old_disk['host_id'],
                    storage_pool_id=old_disk['storage_pool_id'],
                    storage_pool_type=old_disk['storage_pool_type'],
                    storage_vol_id=disk["new_volume_id"],
                    type_code="file",  # 默认文件类型
                    device="disk",     # 默认磁盘设备
                    dev="vda",         # 默认设备名（可根据实际情况调整）
                    bus="virtio",      # 默认总线类型
                    qemu_type="qcow2", # 默认磁盘格式
                    boot_order=0,      # 默认引导顺序
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now(),
                )
                session.add(new_domain_disk)
            session.commit()

            # 直接赋值给form["disk"]，便于后续XML拼接
            form["disk"] = new_disks


            # 查询网络
            domain_interfaces = session.query(DomainInterface).filter(DomainInterface.domain_id == vm_id).all()
            networks_form = []
            for iface in domain_interfaces:
                old_switch_port = session.query(SwitchPorts).filter(SwitchPorts.id == iface.switch_port_id).first()
                if not old_switch_port:
                    continue
                
                switch = session.query(Switch).filter(Switch.id == old_switch_port.switchs_id).first()
                switch_name = switch.ovs_name
                # 新建端口名
                new_port_name = generate_unique_port_name()
                # 新建 SwitchPorts
                new_switch_port = SwitchPorts(
                    name=new_port_name,
                    switchs_id=old_switch_port.switchs_id,
                    switch_port_group_id=old_switch_port.switch_port_group_id,
                    gate_way=old_switch_port.gate_way,
                    next_hop=old_switch_port.next_hop,
                    ip=old_switch_port.ip,
                    ip_type=old_switch_port.ip_type,
                    netmask=old_switch_port.netmask,
                    mode=old_switch_port.mode,
                    pnics=old_switch_port.pnics,
                    ports_num=old_switch_port.ports_num,
                    status=old_switch_port.status,
                    extra_config=old_switch_port.extra_config,
                    is_physical=old_switch_port.is_physical,
                    port_type=old_switch_port.port_type,
                    interface=old_switch_port.interface,
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now(),
                )
                session.add(new_switch_port)
                session.flush()  # 获取新端口id

                # 新建 MAC
                new_mac = generate_random_mac()

                # 新建 DomainInterface
                new_domain_interface = DomainInterface(
                    domain_id=new_domain_id,
                    type_code=iface.type_code,
                    bridge=iface.bridge,
                    mac=new_mac,
                    ip=iface.ip,
                    model=iface.model,
                    driver=iface.driver,
                    network_strategy=iface.network_strategy,
                    vf_name=iface.vf_name,
                    vf_address=iface.vf_address,
                    vlan_id=iface.vlan_id,
                    mtu=iface.mtu,
                    switch_port_id=new_switch_port.id,
                )
                session.add(new_domain_interface)

                # 封装参数用于后续虚拟机创建
                networks_form.append({
                    "type_code": iface.type_code,
                    "bridge": iface.bridge,
                    "mac": new_mac,
                    "ip": iface.ip,
                    "model": iface.model,
                    "driver": iface.driver,
                    "network_strategy": iface.network_strategy,
                    "vf_name": iface.vf_name,
                    "vf_address": iface.vf_address,
                    "vlan_id": iface.vlan_id,
                    "mtu": iface.mtu,
                    "switch_port_id": str(new_switch_port.id),
                    "switch_port_name": new_port_name,
                    "network":switch_name,
                    "port_name":new_port_name,
                    "switch_id": str(old_switch_port.switchs_id),
                    "switch_port_group_id": str(old_switch_port.switch_port_group_id) if old_switch_port.switch_port_group_id else None,
                    # 其他需要的字段
                })
            session.commit()
            form["interface_info"] = networks_form
                
            
            # 子任务链：创建磁盘 + 更新数据库（使用默认队列）
            disk_chain = (
                clone_disk.s(new_disks).set(queue=queue)
            )
            
            
            # 构建子任务组
            tasks = group([
                disk_chain,
            ])
            form["old_vm_name"] = vm_name
            form["vm_name"] = new_vm_name
            form["domain_id"] = new_domain_id
            # 创建最终回调链：先执行 create_vm_callback，再执行 update_database_callback（使用默认队列）
            final_chain = (
                clone_vm.s(form=form).set(queue=queue) |
                clone_vm_callback.s().set(queue=QUEUE_NAME)
            )

            # 启动 chord 任务
            result = chord(tasks)(final_chain)
            
            # result = (clone_vm.apply_async(
            #                             (form),
            #                             queue=queue,
            #                             link=clone_vm_callback.s().set(queue=QUEUE_NAME)))

       
        return {"msg": "ok", "code": 200}
    
    @role_required(("sysadm","supadm"))
    @post(_path="/v5/vm/op/link/clone", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_full_link_vm(self, form):
        """
        克隆虚拟机
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        vm_id = form.get("vm_id", "")
        vm_name = form.get("vm_name", [])
        form["role"] = role
        form["username"] = username
        form["clone_tag"] = "link"
        
        with self.session_scope() as session:
            domain = session.query(Domain).filter(Domain.id == vm_id).first()
            host_ip = domain.host.ip
            queue = "queue_" + host_ip
            
            # 克隆虚拟机数据库数据创建
            # 查询原虚拟机
            if not domain:
                return {"status": "200", "msg": "原虚拟机不存在"}

            # 复制原虚拟机所有字段
            domain_data = domain.to_dict() if hasattr(domain, 'to_dict') else domain.__dict__.copy()

            # 生成新 id、uuid、name
            new_domain_id = str(uuid.uuid4())
            new_uuid = str(uuid.uuid4())
            new_vm_name = generate_unique_vm_name(session, domain.name)

            # 更新必要字段
            domain_data["id"] = new_domain_id
            domain_data["uuid"] = new_uuid
            domain_data["name"] = new_vm_name
            domain_data["status"] = "creating"
            domain_data["domainname"] = new_vm_name
            # 其他如 created_at、operation_time 可重置为 now

            # 移除 SQLAlchemy 内部字段
            domain_data.pop("_sa_instance_state", None)

            # 新建并插入
            new_domain = Domain(**domain_data)
            session.add(new_domain)
            session.commit()
            
            
            
             # 克隆磁盘部分
            domain_disks = session.query(DomainDisk).filter(DomainDisk.domain_id == vm_id).all()
            new_disks = []
            for disk in domain_disks:
                volume = session.query(StorageVolume).filter(StorageVolume.id == disk.storage_vol_id).first()
                if not volume:
                    continue
                new_name = generate_unique_volume_name(session, volume.name)
                fmt = volume.volume_type or "qcow2"
                new_path = get_new_volume_path(volume.path, new_name, fmt)
                new_volume = StorageVolume(
                    name=new_name,
                    storage_pool_id=volume.storage_pool_id,
                    path=new_path,
                    volume_type=fmt,
                    protocol_type=volume.protocol_type,
                    join_type=volume.join_type,
                    encrypt=volume.encrypt,
                    status=volume.status,
                    capacity=volume.capacity,
                    allocation=volume.allocation,
                    preallocation=volume.preallocation,
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now(),
                    remark="克隆卷",
                )
                session.add(new_volume)
                session.flush()  # 获取新卷id
                old_path = get_old_volume_path(volume.path, volume.name, volume.volume_type)
                new_disks.append({
                    "old_domain_disk": disk.to_dict(),
                    "old_path": old_path,
                    "new_volume_id": new_volume.id,
                    "new_volume_name": new_name,
                    "new_path": new_path,
                    "volume_type": fmt,
                    "clone_type": "full",  # 默认完整克隆
                    "storage_pool_id": volume.storage_pool_id,
                    # 直接补充XML拼接所需参数
                    "disk_name": new_name,
                    "path": new_path,
                    "storage_type_code": fmt,
                    "disk_type": disk.type_code,
                    "bus": disk.bus,
                    "dev": disk.dev,
                    "device": disk.device,
                    "qemu_type": disk.qemu_type,
                    "boot_order": disk.boot_order,
                    "capacity": volume.capacity,
                    "allocation": volume.allocation,
                    "clone_type":"linked",
                })
            session.commit()

            # 建立新DomainDisk关联
            for disk in new_disks:
                old_disk = disk["old_domain_disk"]
                new_domain_disk = DomainDisk(
                    domain_id=new_domain_id,  # 新虚拟机id
                    host_id=old_disk['host_id'],
                    storage_pool_id=old_disk['storage_pool_id'],
                    storage_pool_type=old_disk['storage_pool_type'],
                    storage_vol_id=disk["new_volume_id"],
                    type_code="file",  # 默认文件类型
                    device="disk",     # 默认磁盘设备
                    dev="vda",         # 默认设备名（可根据实际情况调整）
                    bus="virtio",      # 默认总线类型
                    qemu_type="qcow2", # 默认磁盘格式
                    boot_order=0,      # 默认引导顺序
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now(),
                )
                session.add(new_domain_disk)
            session.commit()

            # 直接赋值给form["disk"]，便于后续XML拼接
            form["disk"] = new_disks


            # 查询网络
            domain_interfaces = session.query(DomainInterface).filter(DomainInterface.domain_id == vm_id).all()
            networks_form = []
            for iface in domain_interfaces:
                old_switch_port = session.query(SwitchPorts).filter(SwitchPorts.id == iface.switch_port_id).first()
                if not old_switch_port:
                    continue
                
                switch = session.query(Switch).filter(Switch.id == old_switch_port.switchs_id).first()
                switch_name = switch.ovs_name
                # 新建端口名
                new_port_name = generate_unique_port_name()
                # 新建 SwitchPorts
                new_switch_port = SwitchPorts(
                    name=new_port_name,
                    switchs_id=old_switch_port.switchs_id,
                    switch_port_group_id=old_switch_port.switch_port_group_id,
                    gate_way=old_switch_port.gate_way,
                    next_hop=old_switch_port.next_hop,
                    ip=old_switch_port.ip,
                    ip_type=old_switch_port.ip_type,
                    netmask=old_switch_port.netmask,
                    mode=old_switch_port.mode,
                    pnics=old_switch_port.pnics,
                    ports_num=old_switch_port.ports_num,
                    status=old_switch_port.status,
                    extra_config=old_switch_port.extra_config,
                    is_physical=old_switch_port.is_physical,
                    port_type=old_switch_port.port_type,
                    interface=old_switch_port.interface,
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now(),
                )
                session.add(new_switch_port)
                session.flush()  # 获取新端口id

                # 新建 MAC
                new_mac = generate_random_mac()

                # 新建 DomainInterface
                new_domain_interface = DomainInterface(
                    domain_id=new_domain_id,
                    type_code=iface.type_code,
                    bridge=iface.bridge,
                    mac=new_mac,
                    ip=iface.ip,
                    model=iface.model,
                    driver=iface.driver,
                    network_strategy=iface.network_strategy,
                    vf_name=iface.vf_name,
                    vf_address=iface.vf_address,
                    vlan_id=iface.vlan_id,
                    mtu=iface.mtu,
                    switch_port_id=new_switch_port.id,
                )
                session.add(new_domain_interface)

                # 封装参数用于后续虚拟机创建
                networks_form.append({
                    "type_code": iface.type_code,
                    "bridge": iface.bridge,
                    "mac": new_mac,
                    "ip": iface.ip,
                    "model": iface.model,
                    "driver": iface.driver,
                    "network_strategy": iface.network_strategy,
                    "vf_name": iface.vf_name,
                    "vf_address": iface.vf_address,
                    "vlan_id": iface.vlan_id,
                    "mtu": iface.mtu,
                    "switch_port_id": str(new_switch_port.id),
                    "switch_port_name": new_port_name,
                    "network":switch_name,
                    "port_name":new_port_name,
                    "switch_id": str(old_switch_port.switchs_id),
                    "switch_port_group_id": str(old_switch_port.switch_port_group_id) if old_switch_port.switch_port_group_id else None,
                    # 其他需要的字段
                })
            session.commit()
            form["interface_info"] = networks_form
                
            
            # 子任务链：创建磁盘 + 更新数据库（使用默认队列）
            disk_chain = (
                clone_disk.s(new_disks).set(queue=queue)
            )
            
            
            # 构建子任务组
            tasks = group([
                disk_chain,
            ])

            form["old_vm_name"] = vm_name
            form["vm_name"] = new_vm_name
            form["domain_id"] = new_domain_id
            # 创建最终回调链：先执行 create_vm_callback，再执行 update_database_callback（使用默认队列）
            final_chain = (
                clone_vm.s(form=form).set(queue=queue) |
                clone_vm_callback.s().set(queue=QUEUE_NAME)
            )

            # 启动 chord 任务
            result = chord(tasks)(final_chain)
            
            # result = (clone_vm.apply_async(
            #                             (form),
            #                             queue=queue,
            #                             link=clone_vm_callback.s().set(queue=QUEUE_NAME)))

       
        return {"msg": "ok", "code": 200}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_delete(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        host = form.get("host_ip", "")
        ids = form.get("ids", [])
        names = form.get("names", [])

        failed_vms = []
        success_count = 0


        with self.session_scope() as session:
            
            # operator权限检查
            if role == "operator":
                vms_to_check = session.query(Domain).filter(Domain.id.in_(ids)).all()
                allowed, result = operator_can_operate_vms(session, username, vms_to_check,"删除")
                if not allowed:
                    return result
            
            
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "deleting"},
                synchronize_session=False
            )
            session.commit()
            
            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]
        # 为每个虚拟机添加role和username信息
        for vm in vms:
            vm['role'] = role
            vm['username'] = username

        try:
            # 异步调用主任务，传入需要分发的目标节点信息
            distach_op_vm_del.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机删除操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200,"data":"虚拟机删除操作完毕"}

        # try:
        #     queue = "queue_" + host
        #     # libvirtClient = LClient(host=host)

        #     for _id, vm_name in zip(ids, names):
        #         try:
        #             # 删除虚拟机及
        #             try:
        #                 delete_form = {
        #                     "vm_name": vm_name,
        #                     "host": host,
        #                     "role": role,
        #                     "username": username,
        #                     "vm_id": _id
        #                 }
        #                 result = (delete_vm.apply_async((delete_form,),
        #                                                  queue=queue,
        #                                                  link=delete_vm_callback.s().set(queue=QUEUE_NAME)))
        #                 # libvirtClient.del_vm(libvirtClient, vm_name)
        #             except Exception as e:
        #                 if "Domain not found" in str(e):
        #                     print(f"虚拟机 {vm_name} 不存在，继续删除数据库记录")
        #                 else:
        #                     raise e

        #             # 删除数据库记录
        #             with self.session_scope() as session:
        #                 # 判断用户和虚拟机的绑定关系
        #                 # 1. 判断用户和虚拟机的绑定关系
        #                 user_vm_assignment = session.query(UserVmAssignment).filter(
        #                     UserVmAssignment.vm_id == _id
        #                 ).first()
        #                 if user_vm_assignment:
        #                     user_id = user_vm_assignment.user_id
        #                     # 2. 获取用户配额信息
        #                     user_quota = session.query(UserQuota).filter(UserQuota.user_id == user_id).first()
        #                     # 3. 获取虚拟机资源信息
        #                     domain = session.query(Domain).filter(Domain.id == _id).first()
        #                     if user_quota and domain:
        #                         # 扣减配额（防止负数）
        #                         user_quota.cpu_used = max((user_quota.cpu_used or 0) - (domain.vcpu or 0), 0)
        #                         user_quota.memory_used = max((user_quota.memory_used or 0) - (domain.memory or 0), 0)
                        
                        
        #                     # 删除用户和虚拟机的绑定关系
        #                     session.query(UserVmAssignment).filter(UserVmAssignment.vm_id == _id).delete()

        #                 # 删除虚拟机和磁盘的绑定关系，磁盘本身不做删除
        #                 session.query(DomainDisk).filter(DomainDisk.domain_id == _id).delete()
                        
        #                 # 查询和虚拟机绑定的网络端口id
        #                 switch_port_ids_info = session.query(DomainInterface.switch_port_id)\
        #                                     .filter(DomainInterface.domain_id == _id)\
        #                                     .all()  # 返回的是元组列表，如 [(1,), (2,)]
        #                 # 提取纯 ID 列表
        #                 switch_port_ids = [id[0] for id in switch_port_ids_info if id[0] is not None]
                        
        #                 # 批量删除 switch_port 表中的记录
        #                 # 删除相关的网络端口的数据，
        #                 # 网络端口只有在虚拟机运行的时候才会实际创建，所以虚拟机删除的时候将网络端口数据删除
        #                 if switch_port_ids:
        #                     deleted_count = session.query(SwitchPorts)\
        #                                         .filter(SwitchPorts.id.in_(switch_port_ids))\
        #                                         .delete(synchronize_session=False)
        #                 # 删除虚拟机和网络端口的绑定关系                      
        #                 session.query(DomainInterface).filter(DomainInterface.domain_id == _id).delete()
                        
                        
                        
        #                 # 删除对应的虚拟机数据
        #                 session.query(Domain).filter(Domain.id == _id).delete()
        #                 session.commit()

        #             success_count += 1
        #             new_logger.log(
        #                 self.username, "虚拟机", "删除虚拟机", "成功", role,
        #                 f"删除虚拟机: {vm_name} 成功"
        #             )

        #         except Exception as e:
        #             failed_vms.append(vm_name)
        #             print(f"删除虚拟机 {vm_name} 失败: {str(e)}")
        #             new_logger.log(
        #                 self.username, "虚拟机", "删除虚拟机", "失败", role,
        #                 f"删除虚拟机: {vm_name} 失败, 错误: {str(e)}"
        #             )
        #             continue

        #     if failed_vms:
        #         return {
        #             "msg": f"部分虚拟机删除失败: {', '.join(failed_vms)}",
        #             "code": 206,
        #             "success_count": success_count,
        #             "failed_vms": failed_vms
        #         }

        #     return {"msg": "ok", "code": 200}

        # except Exception as e:
        #     print(f"批量删除虚拟机失败: {str(e)}")
        #     traceback.print_exc()
        #     return {"msg": f"批量删除虚拟机失败: {str(e)}", "code": 500}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/op/open", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_open(self, form):
        """
        开机批量
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        ids = form.get("ids", [])
        
        with self.session_scope() as session:
            # operator权限检查
            if role == "operator":
                vms_to_check = session.query(Domain).filter(Domain.id.in_(ids)).all()
                allowed, result = operator_can_operate_vms(session, username, vms_to_check)
                if not allowed:
                    return result
            
            # 批量更新虚拟机状态为 starting
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "starting"},
                synchronize_session=False
            )
            session.commit()
            
            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]
        # 为每个虚拟机添加role和username信息
        for vm in vms:
            vm['role'] = role
            vm['username'] = username

        try:
            # 异步调用主任务，传入需要分发的目标节点信息
            distach_op_vm_open.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机开机操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200,"data":"虚拟机开机操作完毕"}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/op/close", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_close(self, form):
        """
        关机批量
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        ids = form.get("ids", [])
        
        with self.session_scope() as session:
            # operator权限检查
            if role == "operator":
                vms_to_check = session.query(Domain).filter(Domain.id.in_(ids)).all()
                allowed, result = operator_can_operate_vms(session, username, vms_to_check)
                if not allowed:
                    return result
            
            # 批量更新虚拟机状态为 shutdown
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutdown"},
                synchronize_session=False
            )
            session.commit()
            
            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]
        # 为每个虚拟机添加role和username信息
        for vm in vms:
            vm['role'] = role
            vm['username'] = username

        try:
            distach_op_vm_close.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机关机操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机关机操作完毕"}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/op/destroy", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_destroy(self, form):
        """
        批量强制关机
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        ids = form.get("ids", [])

        with self.session_scope() as session:
            # operator权限检查
            if role == "operator":
                vms_to_check = session.query(Domain).filter(Domain.id.in_(ids)).all()
                allowed, result = operator_can_operate_vms(session, username, vms_to_check)
                if not allowed:
                    return result
            
            # 批量更新虚拟机状态为 shutdown
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutdown"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]
        # 为每个虚拟机添加role和username信息
        for vm in vms:
            vm['role'] = role
            vm['username'] = username

        try:
            distach_op_vm_destroy.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机强制关机操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机强制关机操作完毕"}


    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/op/pause", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_pause(self, form):
        """
        批量暂停
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        ids = form.get("ids", [])   

        with self.session_scope() as session:
            # operator权限检查
            if role == "operator":
                vms_to_check = session.query(Domain).filter(Domain.id.in_(ids)).all()
                allowed, result = operator_can_operate_vms(session, username, vms_to_check)
                if not allowed:
                    return result
            
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "pausing"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]
        # 为每个虚拟机添加role和username信息
        for vm in vms:
            vm['role'] = role
            vm['username'] = username

        try:
            distach_op_vm_pause.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机暂停操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机暂停操作完毕"}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/op/recover", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_recover(self, form):
        """
        批量恢复
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        ids = form.get("ids", [])   

        with self.session_scope() as session:
            # operator权限检查
            if role == "operator":
                vms_to_check = session.query(Domain).filter(Domain.id.in_(ids)).all()
                allowed, result = operator_can_operate_vms(session, username, vms_to_check)
                if not allowed:
                    return result
                    
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "recover"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]
        # 为每个虚拟机添加role和username信息
        for vm in vms:
            vm['role'] = role
            vm['username'] = username

        try:
            distach_op_vm_recover.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机恢复操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机恢复操作完毕"}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/op/reboot", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_reboot(self, form):
        """
        批量重启
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        ids = form.get("ids", [])   

        with self.session_scope() as session:
            # operator权限检查
            if role == "operator":
                vms_to_check = session.query(Domain).filter(Domain.id.in_(ids)).all()
                allowed, result = operator_can_operate_vms(session, username, vms_to_check)
                if not allowed:
                    return result
                    
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "reboot"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]
        # 为每个虚拟机添加role和username信息
        for vm in vms:
            vm['role'] = role
            vm['username'] = username

        try:
            distach_op_vm_reboot.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机重启操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机重启操作完毕"}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/op/force/restart", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_force_restart(self, form):
        """
        批量强制重启
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        ids = form.get("ids", [])   

        with self.session_scope() as session:
            # operator权限检查
            if role == "operator":
                vms_to_check = session.query(Domain).filter(Domain.id.in_(ids)).all()
                allowed, result = operator_can_operate_vms(session, username, vms_to_check)
                if not allowed:
                    return result
                    
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "restart"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]

        try:
            distach_op_vm_restart.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机强制重启操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机强制重启操作完毕"}

    # @post(_path="/v5/vm/delete/{vm_id}", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    # def hci_post_vm_delete_vm(self, vm_id, form):
    #     role = self.get_cookie('role', "")

    #     vm_id = vm_id
    #     host = form.get("host", "")
    #     vm_name = form.get("vm_name", [])

    #     client = LibvirtClient(host)
    #     libvirtClient = LClient(host=host)
    #     try:
    #         r = libvirtClient.del_vm(libvirtClient, vm_name)

    #         new_logger.log(
    #             self.username, "虚拟机", "删除虚拟机", "成功", role,
    #             "删除虚拟机: {},成功".format(vm_name)
    #         )
    #     except Exception as e:
    #         print(f"删除虚拟机：{vm_name} 报错：", e)
    #         new_logger.log(
    #             self.username, "虚拟机", "删除虚拟机", "失败", role,
    #             "删除虚拟机: {},成功".format(vm_name)
    #         )
    #         print("删除报错：", e)
    #         traceback.print_exc()
    #         return {"msg": "删除虚拟机失败", "code": 500}

    #     return {"msg": "删除虚拟机成功", "code": 200}

    @deprecated_api()
    @post(_path="/v5/vm/op/open/{vm_id}", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_open_vm(self, vm_id, form):
        """
        开机
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.start_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "开机", "成功", role,
                "虚拟机开机: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "开机", "失败", role,
                "虚拟机开机: {},失败".format(vm_name)
            )
            print("开机", e)
            traceback.print_exc()
            return {"msg": "虚拟机开机操作失败", "code": 500}

        return {"msg": "虚拟机开机操作成功", "code": 200}

    @deprecated_api(message="该接口已禁用")
    @post(_path="/v5/vm/op/close/{vm_id}", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_close_vm(self, vm_id, form):
        """
        关机
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.stop_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "关机", "成功", role,
                "虚拟机关机: {},成功".format(vm_name)
            )
        except Exception as e:
            new_logger.log(
                self.username, "虚拟机", "关机", "失败", role,
                "虚拟机关机: {},失败".format(vm_name)
            )
            print("关机", e)
            traceback.print_exc()
            return {"msg": "虚拟机关机操作失败", "code": 500}

        return {"msg": "虚拟机关机操作成功", "code": 200}

    @deprecated_api(message="该接口已禁用")
    @post(_path="/v5/vm/op/destroy/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_destroy_vm(self, vm_id, form):
        """
        强制关机
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.force_stop_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "强制关机", "成功", role,
                "虚拟机强制关机: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "强制关机", "失败", role,
                "虚拟机强制关机: {},失败".format(vm_name)
            )
            print("强制关机", e)
            traceback.print_exc()
            return {"msg": "虚拟机强制关机操作失败", "code": 500}

        return {"msg": "虚拟机强制关机操作成功", "code": 200}

    @deprecated_api(message="该接口已禁用")
    @post(_path="/v5/vm/op/pause/{vm_id}", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_pause_vm(self, vm_id, form):
        """
        暂停
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.suspend_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "暂停", "成功", role,
                "虚拟机暂停: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "暂停", "失败", role,
                "虚拟机暂停: {},失败".format(vm_name)
            )
            print("暂停", e)
            traceback.print_exc()
            return {"msg": "虚拟机暂停操作失败", "code": 500}

        return {"msg": "虚拟机暂停操作成功", "code": 200}

    @deprecated_api(message="该接口已禁用")
    @post(_path="/v5/vm/op/recover/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_recover_vm(self, vm_id, form):
        """
        恢复
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.resume_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "恢复", "成功", role,
                "虚拟机恢复: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "恢复", "失败", role,
                "虚拟机恢复: {},失败".format(vm_name)
            )
            print("恢复", e)
            traceback.print_exc()
            return {"msg": "虚拟机恢复操作失败", "code": 500}

        return {"msg": "虚拟机恢复操作成功", "code": 200}

    @deprecated_api(message="该接口已禁用")
    @post(_path="/v5/vm/op/reboot/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_reboot_vm(self, vm_id, form):
        """
        重启
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.reboot_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "重启", "成功", role,
                "虚拟机重启: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "重启", "失败", role,
                "虚拟机重启: {},失败".format(vm_name)
            )
            print("重启", e)
            traceback.print_exc()
            return {"msg": "虚拟机重启操作失败", "code": 500}

        return {"msg": "虚拟机重启操作成功", "code": 200}

    @deprecated_api(message="该接口已禁用")
    @post(_path="/v5/vm/op/force/restart/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_force_restart_vm(self, vm_id, form):
        """
        强制重启
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.force_reboot_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "强制重启", "成功", role,
                "虚拟机强制重启: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "强制重启", "失败", role,
                "虚拟机强制重启: {},失败".format(vm_name)
            )
            print("强制重启", e)
            traceback.print_exc()
            return {"msg": "虚拟机强制重启操作失败", "code": 500}

        return {"msg": "虚拟机强制重启操作成功", "code": 200}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/snapshot/create", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_snapshot_create(self, form):
        """
        创建快照
        """
        print(form)
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        form["username"] = username
        form["role"] = role
        vm_id = form.get("vm_id", "")
        vm_name = form.get("vm_name", [])
        snapshot_name = form.get("snapshot_name", "")

        try:
            with self.session_scope() as session:
                
                if role == "operator":
                    result, msg = operator_can_operate_vm(session, username, vm_id)
                    if not result:
                        return {"msg": msg, "code": 200}
                
                vm = session.query(Domain).filter(Domain.id == vm_id).first()
                
                
                # if vm.status == "running":
                #     return {"msg": "虚拟机正在运行，无法创建快照", "code": 200}
                
                form["vm_name"] = vm.name
                host_ip = vm.host.ip
                queue = "queue_" + host_ip

            result = snapshot_list.apply_async(args=[form], 
                                               queue=queue, 
                                               link=create_vm_snapshot_callback.s().set(queue=QUEUE_NAME))
            res = result.get(timeout=10)
            if snapshot_name and any(snap.get("name") == snapshot_name for snap in res):
                return {"code": "200", "msg": "快照已存在"}
    
            create_vm_snapshot.apply_async(args=[form], queue=queue)
            # new_logger.log(
            #     self.username, "虚拟机", "创建快照", "成功", role,
            #     "虚拟机创建快照: {},成功".format(vm_name)
            # )
        except  Exception as e:
            # new_logger.log(
            #     self.username, "虚拟机", "创建快照", "失败", role,
            #     "虚拟机创建快照: {},失败".format(vm_name)
            # )
            traceback.print_exc()
            return {"msg": "虚拟机创建快照操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200}

    @role_required(("sysadm","supadm","operator"))
    @delete(_path="/v5/vm/snapshot/delete", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_snapshot_delete(self, form):
        """
        删除快照
        """
        print(form)
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        form["username"] = username
        form["role"] = role
        snapshot_names = form.get("snapshot_names", "")
        vm_name = form.get("vm_name", [])
        vm_id = form.get("vm_id", "")

        try:
            with self.session_scope() as session:
                
                if role == "operator":
                    result, msg = operator_can_operate_vm(session, username, vm_id)
                    if not result:
                        return {"msg": msg, "code": 200}
                
                vm = session.query(Domain).filter(Domain.id == vm_id).first()
               
                
                form["vm_name"] = vm.name
                host_ip = vm.host.ip
                queue = "queue_" + host_ip
            for snapshot_name in snapshot_names:
                form["snapshot_name"] = snapshot_name
                delete_vm_snapshot.apply_async(args=[form], 
                                               queue=queue,
                                               link=delete_vm_snapshot_callback.s().set(queue=QUEUE_NAME))
            # new_logger.log(
            #     self.username, "虚拟机", "删除快照", "成功", role,
            #     "虚拟机删除快照: {},成功".format(vm_name)
            # )
            return {"msg": "ok", "code": 200}
            
        except  Exception as e:
            print(e)
            # new_logger.log(
            #     self.username, "虚拟机", "删除快照", "失败", role,
            #     "虚拟机删除快照: {},失败".format(vm_name)
            # )
            traceback.print_exc()
            return {"msg": "虚拟机删除快照操作失败", "code": 200}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/snapshot/restore", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_snapshot_restore(self, form):
        """
        从快照还原虚拟机
        """
        print(form)
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        vm_name = form.get("vm_name", [])
        vm_id = form.get("vm_id", "")
        form["username"] = username
        form["role"] = role

        try:
            with self.session_scope() as session:
                
                if role == "operator":
                    result, msg = operator_can_operate_vm(session, username, vm_id)
                    if not result:
                        return {"msg": msg, "code": 200}
                
                vm = session.query(Domain).filter(Domain.id == vm_id).first()   
                form["vm_name"] = vm.name
                host_ip = vm.host.ip
                queue = "queue_" + host_ip
            restore_vm_snapshot.apply_async(args=[form], 
                                            queue=queue,
                                            link=restore_vm_snapshot_callback.s().set(queue=QUEUE_NAME))
            # new_logger.log(
            #     self.username, "虚拟机", "从快照还原", "成功", role,
            #     "虚拟机从快照还原: {},成功".format(vm_name)
            # )
        except Exception as e:
            # new_logger.log(
            #     self.username, "虚拟机", "从快照还原", "失败", role,
            #     "虚拟机从快照还原: {},失败".format(vm_name)
            # )
            traceback.print_exc()
            return {"msg": "虚拟机从快照还原操作失败", "code": 500}

        return {"msg": "ok", "code": 200}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/snapshot/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_snapshot_list(self, form):
        """
        快照列表
        """
        print(form)
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        vm_id = form.get("vm_id", "")
        vm_name = form.get("vm_name", [])

        try:
            with self.session_scope() as session:   
                
                if role == "operator":
                    result, msg = operator_can_operate_vm(session, username, vm_id)
                    if not result:
                        return {"msg": msg, "code": 200}
                
                vm = session.query(Domain).filter(Domain.id == vm_id).first()
                form["vm_name"] = vm.name
                host_ip = vm.host.ip
                queue = "queue_" + host_ip
            result = snapshot_list.apply_async(args=[form], queue=queue)
            res = result.get(timeout=10)


        except  Exception as e:

            traceback.print_exc()
            return {"msg": e, "code": 500, "data": res}

        return {"msg": "ok", "code": 200, "data": res}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/recycle/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_recycle_list(self, form):

        # 从 form 中获取参数
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')

        with self.session_scope() as session:
            
            query = session.query(Domain).filter(Domain.domain_recycle_id == "1") # 回收站 0为不在回收站中，1为在回收站中

            # operator权限检查
            if role == "operator":
                # 查询用户信息
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return {"msg": "用户不存在", "code": 404}
                
                # 通过UserVmAssignment表联合查询该用户有权限的虚拟机
                query = query.join(UserVmAssignment, 
                                Domain.id == UserVmAssignment.vm_id)\
                          .filter(UserVmAssignment.user_id == user.id)
            
            # 搜索功能，根据指定字段进行搜索（这里假设搜索的字段为 name）
            if search_str:
                query = query.filter(Domain.name.ilike(f"%{search_str}%")).filter(Domain.domain_recycle_id == "1")

            # 排序功能
            if order_by:
                if order_type == 'asc':
                    query = query.order_by(asc(getattr(Domain, order_by)))
                else:
                    query = query.order_by(desc(getattr(Domain, order_by)))

            # 计算总记录数
            total_records = query.count()

            # 分页
            domains = query.limit(per_page).offset((page - 1) * per_page).all()

            # 将结果转换为字典列表
            domain_list = [Domain.to_dict(domain) for domain in domains]

            # 计算总页数
            total_pages = ceil(total_records / per_page)

            # 返回分页信息和数据
        return {
            "msg": "获取虚机列表成功",
            "code": 200,
            "total": total_records,
            "data": domain_list
        }

        # return {"msg": "获取虚机列表失败", "code": 500}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/recycle/movein", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_recycle_movein(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        names = form.get("names", [])
        ids = form.get("ids", [])
        for _id, _name in zip(ids, names):
            try:
                
                with self.session_scope() as session:
                    
                    if role == "operator":
                        result, msg = operator_can_operate_vm(session, username, _id)
                        if not result:
                            return {"msg": msg, "code": 200}
                    
                    session.query(Domain).filter(Domain.id == _id).update({Domain.domain_recycle_id : "1"})
                    session.commit()
                new_logger.log(
                    username, "虚拟机", "回收站", "移动", role,
                    f"将虚拟机 {_name} 移动到回收站"
                )
            except Exception as e:
                new_logger.log(
                    username, "虚拟机", "回收站", "移动", role,
                    f"将虚拟机 {_name} 移动到回收站失败"
                )

        return {"msg": "ok", "code": 200}

    @role_required()
    @post(_path="/v5/vm/op/migration", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_migration(self, form):
        """
        虚拟机迁移接口
        请求参数:
        {
            "names": ["vm1", "vm2"],  # 虚拟机名称列表
            "ids": ["id1", "id2"],    # 虚拟机ID列表
            "new_host_id": "target_host_id",  # 目标主机ID
            "migration_type": "live"  # 可选: live(热迁移) 或 offline(冷迁移)，默认live
        }
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        names = form.get("names", [])
        ids = form.get("ids", [])
        new_host_id = form.get("new_host_id", "")
        migration_type = form.get("migration_type", "live")

        if not names or not ids or not new_host_id:
            return {"msg": "缺少必要参数", "code": 400}

        if len(names) != len(ids):
            return {"msg": "虚拟机名称和ID数量不匹配", "code": 400}

        try:
            # 构建迁移任务数据
            migration_tasks = []

            with self.session_scope() as session:
                # 获取目标主机信息
                target_host = session.query(Host).filter(Host.id == new_host_id).first()
                if not target_host:
                    return {"msg": "目标主机不存在", "code": 404}

                target_host_ip = target_host.ip

                # 为每个虚拟机构建迁移任务
                for vm_id, vm_name in zip(ids, names):
                    # 获取虚拟机当前主机信息
                    domain = session.query(Domain).join(Host, Domain.host_id == Host.id).filter(Domain.id == vm_id).first()
                    if not domain:
                        continue

                    source_host_ip = domain.host.ip

                    # 检查是否需要迁移（源主机和目标主机不同）
                    if source_host_ip == target_host_ip:
                        continue

                    migration_task = {
                        "vm_name": vm_name,
                        "vm_id": vm_id,
                        "source_host": source_host_ip,
                        "target_host": target_host_ip,
                        "migration_type": migration_type,
                        "host": {"ip": source_host_ip},  # 用于任务分发
                        "username": username,
                        "role": role
                    }
                    migration_tasks.append(migration_task)

            if not migration_tasks:
                return {"msg": "没有需要迁移的虚拟机", "code": 200}

            # 使用分发任务执行迁移
            result = create_start_vm_migration.apply_async(
                args=[migration_tasks],
                queue=QUEUE_NAME
            )

            # 记录日志
            new_logger.log(
                username, "虚拟机", "迁移", "开始", role,
                f"开始迁移虚拟机: {', '.join(names)} 到主机 {target_host.name}"
            )

            return {
                "msg": f"已开始迁移 {len(migration_tasks)} 个虚拟机",
                "code": 200,
                "task_id": result.id,
                "migration_count": len(migration_tasks)
            }

        except Exception as e:
            import traceback
            traceback.print_exc()
            new_logger.log(
                username, "虚拟机", "迁移", "失败", role,
                f"虚拟机迁移失败: {', '.join(names)}, 错误: {str(e)}"
            )
            return {"msg": f"虚拟机迁移操作失败: {str(e)}", "code": 500}

    @role_required()
    @post(_path="/v5/vm/op/migrate/check", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_migrate_check(self, form):
        """
        检查虚拟机迁移支持
        请求参数:
        {
            "vm_name": "vm1",
            "vm_id": "vm_id",
            "target_host_id": "target_host_id"
        }
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")

        vm_name = form.get("vm_name", "")
        vm_id = form.get("vm_id", "")
        target_host_id = form.get("target_host_id", "")

        if not vm_name or not vm_id or not target_host_id:
            return {"msg": "缺少必要参数", "code": 400}

        try:
            with self.session_scope() as session:
                # 获取虚拟机和目标主机信息
                domain = session.query(Domain).join(Host, Domain.host_id == Host.id).filter(Domain.id == vm_id).first()
                target_host = session.query(Host).filter(Host.id == target_host_id).first()

                if not domain:
                    return {"msg": "虚拟机不存在", "code": 404}
                if not target_host:
                    return {"msg": "目标主机不存在", "code": 404}

                source_host_ip = domain.host.ip
                target_host_ip = target_host.ip

                # 检查迁移支持
                check_form = {
                    "vm_name": vm_name,
                    "source_host": source_host_ip,
                    "target_host": target_host_ip
                }

                # 使用队列执行检查任务
                queue = f"queue_{source_host_ip}"
                result = check_migration_support.apply_async(
                    args=[check_form],
                    queue=queue
                )

                # 等待结果（设置超时）
                check_result = result.get(timeout=30)

                return {
                    "msg": "检查完成",
                    "code": 200,
                    "supported": check_result.get("supported", False),
                    "message": check_result.get("message", ""),
                    "vm_name": vm_name,
                    "source_host": source_host_ip,
                    "target_host": target_host_ip
                }

        except Exception as e:
            import traceback
            traceback.print_exc()
            return {"msg": f"检查迁移支持失败: {str(e)}", "code": 500}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/vm/recycle/moveout", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_recycle_moveout(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        names = form.get("names", [])
        ids = form.get("ids", [])
        for _id, _name in zip(ids, names):
            try:
                with self.session_scope() as session:
                    
                    if role == "operator":
                        result, msg = operator_can_operate_vm(session, username, _id)
                        if not result:
                            return {"msg": msg, "code": 200}
                        
                    session.query(Domain).filter(Domain.id == _id).update({Domain.domain_recycle_id : "0"})
                    session.commit()
                new_logger.log(
                    username, "虚拟机", "回收站", "移出", role,
                    f"将虚拟机 {_name} 移出回收站"
                )
            except Exception as e:
                new_logger.log(
                    username, "虚拟机", "回收站", "移出", role,
                    f"将虚拟机 {_name} 移出回收站失败"
                )

        return {"msg": "ok", "code": 200}
